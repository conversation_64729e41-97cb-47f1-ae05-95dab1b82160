2025-06-09 14:21:04,205 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-09 14:21:04,205 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250609_142104.log
2025-06-09 14:21:04,269 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101\dist\TLT Group Management System\_internal
2025-06-09 14:21:04,322 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\dist\TLT Group Management System\_internal\resources\TLT icon.ico
2025-06-09 14:21:04,333 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 14:21:04,333 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 14:21:04,333 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 14:21:04,341 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-09 14:21:04,343 - root - INFO - Database check completed successfully
2025-06-09 14:21:04,344 - root - INFO - Running in packaged environment. App dir: C:\Users\<USER>\Desktop\music project\Accounting101\dist\TLT Group Management System\_internal\modules\ui
2025-06-09 14:21:04,344 - root - INFO - Found potential module directory: C:\Users\<USER>\Desktop\music project\Accounting101\dist\TLT Group Management System\_internal\modules\ui
2025-06-09 14:21:04,344 - root - INFO - Files in C:\Users\<USER>\Desktop\music project\Accounting101\dist\TLT Group Management System\_internal\modules\ui: ['base_module.py', 'customers.py', 'inventory.py', 'license_dialog.py', 'login_dialog.py', 'main_window.py', 'purchases.py', 'reports.py', 'sales.py', 'settings.py', 'suppliers.py']
2025-06-09 14:21:04,344 - root - INFO - Loading module: Sales
2025-06-09 14:21:04,428 - root - INFO - Successfully loaded module: Sales
2025-06-09 14:21:04,428 - root - INFO - Loading module: Customers
2025-06-09 14:21:04,495 - root - INFO - Successfully loaded module: Customers
2025-06-09 14:21:04,495 - root - INFO - Loading module: Suppliers
2025-06-09 14:21:04,502 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-09 14:21:04,503 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-09 14:21:04,517 - root - INFO - Successfully loaded module: Suppliers
2025-06-09 14:21:04,517 - root - INFO - Loading module: Purchases
2025-06-09 14:21:04,555 - root - INFO - Successfully loaded module: Purchases
2025-06-09 14:21:04,556 - root - INFO - Loading module: Inventory
2025-06-09 14:21:04,609 - modules.utils.barcode_generator_new - ERROR - Failed to import PIL modules: cannot import name 'ImageDraw' from 'PIL' (C:\Users\<USER>\Desktop\music project\Accounting101\dist\TLT Group Management System\_internal\PIL\__init__.pyc)
2025-06-09 14:21:04,609 - modules.utils.barcode_generator_new - ERROR - Failed to import or use barcode module: No module named 'barcode'
2025-06-09 14:21:04,609 - modules.utils.barcode_generator_new - WARNING - Barcode module not available, will use fallback implementation
2025-06-09 14:21:04,752 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-09 14:21:04,795 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-09 14:21:04,795 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-09 14:21:04,797 - root - INFO - Successfully loaded module: Inventory
2025-06-09 14:21:04,797 - root - INFO - Loading module: Reports
2025-06-09 14:21:04,809 - root - INFO - Successfully loaded module: Reports
2025-06-09 14:21:04,809 - root - INFO - Loading module: Settings
2025-06-09 14:21:04,869 - root - INFO - Successfully loaded module: Settings
2025-06-09 14:21:04,869 - root - INFO - Application starting in unlocked state
2025-06-09 14:21:04,870 - root - INFO - Initializing security timer
2025-06-09 14:21:04,870 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-09 14:21:04,870 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-09 14:21:04,870 - root - INFO - Security timer initialized successfully
2025-06-09 14:21:04,870 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 14:21:13,798 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 14:21:13,799 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 14:21:14,280 - root - INFO - Changed to tab: Suppliers (index: 2)
2025-06-09 14:21:14,280 - root - INFO - Security delay is set to immediate, requiring password for Suppliers tab
2025-06-09 14:21:15,087 - root - INFO - Changed to tab: Purchases (index: 3)
2025-06-09 14:21:15,087 - root - INFO - Security delay is set to immediate, requiring password for Purchases tab
2025-06-09 14:21:15,622 - root - INFO - Changed to tab: Inventory (index: 4)
2025-06-09 14:21:15,623 - root - INFO - Security delay is set to immediate, requiring password for Inventory tab
2025-06-09 14:21:16,113 - root - INFO - Changed to tab: Reports (index: 5)
2025-06-09 14:21:16,113 - root - INFO - Security delay is set to immediate, requiring password for Reports tab
2025-06-09 14:21:16,869 - root - INFO - Changed to tab: Settings (index: 6)
2025-06-09 14:21:16,869 - root - INFO - Security delay is set to immediate, requiring password for Settings tab
2025-06-09 14:21:17,315 - root - INFO - Changed to tab: Reports (index: 5)
2025-06-09 14:21:17,316 - root - INFO - Security delay is set to immediate, requiring password for Reports tab
2025-06-09 14:21:17,917 - root - INFO - Changed to tab: Settings (index: 6)
2025-06-09 14:21:17,918 - root - INFO - Security delay is set to immediate, requiring password for Settings tab
2025-06-09 14:21:19,046 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 14:21:21,423 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 14:21:21,423 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 14:21:31,590 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 14:21:46,362 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 14:21:46,362 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 14:21:58,014 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 14:22:10,336 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 14:22:10,336 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 14:22:16,005 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 14:22:24,792 - root - INFO - Application closing - performing cleanup
2025-06-09 14:22:24,820 - root - INFO - ===== TLT Group Management System Shutting Down =====
