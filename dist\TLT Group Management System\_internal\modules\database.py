import sqlite3
import os
import stat
import logging
import sys
import json
import tempfile
from pathlib import Path

class Database:
    def __init__(self):
        # Find a suitable database path with multiple fallback options
        self.db_path = self._find_database_path()
        logging.info(f"Selected database path: {self.db_path.absolute()}")

        # Make sure the database directory exists
        self._ensure_db_directory_exists()

        # Try to connect to the database
        self._connect_to_database()

    def _find_database_path(self):
        """Find a suitable database path with multiple fallback options"""
        possible_paths = []

        # Try to load path from config if it exists
        # First check AppData location for packaged app
        if getattr(sys, 'frozen', False):
            appdata_config = os.path.join(os.environ.get('LOCALAPPDATA', ''), 'TLT Group', 'Management System', 'config', 'config.json')
            if os.path.exists(appdata_config):
                try:
                    with open(appdata_config, 'r') as f:
                        config = json.load(f)
                        if 'database' in config and 'path' in config['database']:
                            db_path = Path(config['database']['path'])
                            if not db_path.is_absolute():
                                # For relative paths, use AppData directory
                                db_path = Path(os.path.join(os.environ.get('LOCALAPPDATA', ''), 'TLT Group', 'Management System', 'data', db_path))
                            possible_paths.append(db_path)
                            logging.info(f"Found database path in AppData config: {db_path}")
                except Exception as e:
                    logging.warning(f"Error loading database path from AppData config: {e}")

        # Then check the default config location
        config_path = Path("config/config.json")
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if 'database' in config and 'path' in config['database']:
                        db_path = Path(config['database']['path'])
                        if not db_path.is_absolute():
                            db_path = Path.cwd() / db_path
                        possible_paths.append(db_path)
                        logging.info(f"Found database path in config: {db_path}")
            except Exception as e:
                logging.warning(f"Error loading database path from config: {e}")

        # Add default paths
        possible_paths.extend([
            Path.cwd() / "accounting.db",  # Current working directory
            Path.cwd() / "data" / "accounting.db",  # Data subdirectory
            Path(os.path.expanduser("~")) / "Documents" / "TLT Group" / "accounting.db",  # User's documents
            Path(os.path.join(os.environ.get('LOCALAPPDATA', ''), 'TLT Group', 'Management System', 'data', 'accounting.db')),  # AppData
            Path(tempfile.gettempdir()) / "TLT_Group" / "accounting.db"  # Temp directory
        ])

        # If running as a packaged app, add app-specific paths
        if getattr(sys, 'frozen', False):
            app_dir = Path(os.path.dirname(sys.executable))
            possible_paths.extend([
                app_dir / "accounting.db",  # App directory
                app_dir / "data" / "accounting.db",  # Data subdirectory in app directory
            ])

        # Check each path - if the file exists, use it; otherwise use the first writable directory
        first_writable = None
        for path in possible_paths:
            if path.exists():
                logging.info(f"Found existing database at: {path}")
                return path
            elif first_writable is None and self._is_directory_writable(path.parent):
                first_writable = path

        # If no existing database found, use the first writable location
        if first_writable:
            logging.info(f"Using first writable location for database: {first_writable}")
            return first_writable

        # Last resort - use temp directory
        temp_path = Path(tempfile.gettempdir()) / "TLT_Group" / "accounting.db"
        logging.warning(f"No suitable database location found. Using temp directory: {temp_path}")
        return temp_path

    def _is_directory_writable(self, directory):
        """Check if a directory is writable and create it if it doesn't exist"""
        try:
            # Create the directory if it doesn't exist
            os.makedirs(directory, exist_ok=True)

            # Check if we can write to it
            test_file = directory / "write_test.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return True
        except Exception as e:
            logging.debug(f"Directory {directory} is not writable: {e}")
            return False

    def _ensure_db_directory_exists(self):
        """Make sure the database directory exists and is writable"""
        db_dir = self.db_path.parent
        try:
            # Create the directory if it doesn't exist
            os.makedirs(db_dir, exist_ok=True)
            logging.info(f"Ensured database directory exists: {db_dir}")

            # Check if the directory is writable
            if not os.access(db_dir, os.W_OK):
                try:
                    # Try to make the directory writable
                    current_permissions = os.stat(db_dir).st_mode
                    os.chmod(db_dir, current_permissions | stat.S_IWRITE)
                    logging.info(f"Made database directory writable: {db_dir}")
                except Exception as e:
                    logging.warning(f"Could not make database directory writable: {e}")
        except Exception as e:
            logging.error(f"Error ensuring database directory exists: {e}")

    def _connect_to_database(self):
        """Connect to the database with fallback options"""
        # Check if the database file exists and is read-only
        if self.db_path.exists() and not os.access(self.db_path, os.W_OK):
            try:
                # Try to make the file writable
                current_permissions = os.stat(self.db_path).st_mode
                os.chmod(self.db_path, current_permissions | stat.S_IWRITE)
                logging.info("Made database file writable")
            except Exception as e:
                logging.warning(f"Could not make database writable: {e}")

        # Try to connect with WAL journal mode first
        try:
            self.conn = sqlite3.connect(str(self.db_path.absolute()), timeout=30.0, isolation_level=None)
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.conn.execute("PRAGMA journal_mode = WAL")  # Use Write-Ahead Logging for better concurrency

            self.cursor = self.conn.cursor()
            self.create_tables()
            self.upgrade_schema()
            logging.info("Database initialized successfully with WAL journal mode")
            return
        except sqlite3.OperationalError as e:
            logging.warning(f"Error connecting to database with WAL mode: {e}")

        # Try with DELETE journal mode
        try:
            logging.info("Trying to connect with DELETE journal mode...")
            self.conn = sqlite3.connect(str(self.db_path.absolute()), timeout=30.0, isolation_level=None)
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.conn.execute("PRAGMA journal_mode = DELETE")  # Use DELETE journal mode as fallback

            self.cursor = self.conn.cursor()
            self.create_tables()
            self.upgrade_schema()
            logging.info("Database initialized successfully with DELETE journal mode")
            return
        except sqlite3.OperationalError as e:
            logging.error(f"Error connecting to database with DELETE mode: {e}")

        # If all else fails, try memory database as last resort
        try:
            logging.warning("All database connection attempts failed. Using in-memory database as fallback.")
            self.conn = sqlite3.connect(":memory:", timeout=30.0, isolation_level=None)
            self.conn.execute("PRAGMA foreign_keys = ON")

            self.cursor = self.conn.cursor()
            self.create_tables()
            self.upgrade_schema()
            logging.info("In-memory database initialized successfully")

            # Show warning to user via logging
            logging.warning("WARNING: Using in-memory database. Data will not be saved between sessions!")
        except sqlite3.OperationalError as e:
            logging.critical(f"Critical error: Could not initialize any database: {e}")
            raise Exception(f"Could not initialize database: {e}")

    def check_database(self):
        """Check if the database connection is working"""
        try:
            # Try a simple query to check if the database is working
            self.cursor.execute("SELECT 1")
            result = self.cursor.fetchone()
            if result and result[0] == 1:
                logging.info("Database connection check successful")
                return True
            else:
                logging.error("Database connection check failed: unexpected result")
                return False
        except Exception as e:
            logging.error(f"Database connection check failed: {e}")
            return False

    def create_tables(self):
        """Create database tables if they don't exist"""
        self.cursor.executescript("""
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY,
                product_code TEXT UNIQUE,
                name TEXT NOT NULL,
                quantity INTEGER DEFAULT 0,
                unit_price REAL,
                cost_price REAL,
                category TEXT,
                min_stock INTEGER DEFAULT 0,
                description TEXT
            );

            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                contact TEXT,
                address TEXT,
                balance REAL DEFAULT 0
            );

            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                contact TEXT,
                address TEXT,
                balance REAL DEFAULT 0
            );

            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY,
                date TEXT NOT NULL,
                type TEXT NOT NULL,
                amount REAL,
                description TEXT,
                reference TEXT
            );

            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                total_amount REAL,
                date TEXT,
                vat_rate REAL DEFAULT 0,
                vat_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'Not Paid',
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            );

            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY,
                invoice_id INTEGER,
                product_id INTEGER,
                quantity INTEGER,
                price REAL,
                item_name TEXT,
                description TEXT,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (product_id) REFERENCES inventory (id)
            );

            CREATE TABLE IF NOT EXISTS invoice_deposits (
                id INTEGER PRIMARY KEY,
                invoice_id INTEGER,
                amount REAL NOT NULL,
                date TEXT NOT NULL,
                description TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            );

            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY,
                supplier_id INTEGER,
                total_amount REAL,
                date TEXT,
                status TEXT,
                received_date TEXT,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            );

            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id INTEGER PRIMARY KEY,
                order_id INTEGER,
                product_id INTEGER,
                quantity INTEGER,
                cost_price REAL,
                total_cost REAL,
                FOREIGN KEY (order_id) REFERENCES purchase_orders (id),
                FOREIGN KEY (product_id) REFERENCES inventory (id)
            );
        """)
        self.conn.commit()

    def upgrade_schema(self):
        """Add missing columns to existing tables"""
        try:
            # Add description column to invoice_items if it doesn't exist
            self.cursor.execute("""
                ALTER TABLE invoice_items
                ADD COLUMN description TEXT;
            """)
            self.conn.commit()
        except sqlite3.OperationalError:
            # Column might already exist
            pass

        try:
            # Add discount columns to invoices if they don't exist
            self.cursor.execute("""
                ALTER TABLE invoices
                ADD COLUMN discount_amount REAL DEFAULT 0;
            """)
            self.conn.commit()
        except sqlite3.OperationalError:
            # Column might already exist
            pass

        try:
            # Add discount_type column to invoices
            self.cursor.execute("""
                ALTER TABLE invoices
                ADD COLUMN discount_type TEXT DEFAULT 'amount';
            """)
            self.conn.commit()
        except sqlite3.OperationalError:
            # Column might already exist
            pass

        try:
            # Add deposit_amount column to invoices
            self.cursor.execute("""
                ALTER TABLE invoices
                ADD COLUMN deposit_amount REAL DEFAULT 0;
            """)
            self.conn.commit()
        except sqlite3.OperationalError:
            # Column might already exist
            pass

        try:
            # Add deposit_date column to invoices
            self.cursor.execute("""
                ALTER TABLE invoices
                ADD COLUMN deposit_date TEXT;
            """)
            self.conn.commit()
        except sqlite3.OperationalError:
            # Column might already exist
            pass

    def check_database(self):
        """Check if database is working correctly and print diagnostic information"""
        try:
            # Check inventory table
            self.cursor.execute("SELECT COUNT(*) FROM inventory")
            inventory_count = self.cursor.fetchone()[0]
            print(f"Inventory items count: {inventory_count}")

            # Check if we can insert and retrieve a test record
            test_name = "TEST_ITEM_DELETE_ME"

            # First check if test item exists and delete it
            self.cursor.execute("DELETE FROM inventory WHERE name = ?", (test_name,))

            # Insert test item
            self.cursor.execute("""
                INSERT INTO inventory (name, quantity, unit_price, product_code)
                VALUES (?, ?, ?, ?)
            """, (test_name, 1, 1.0, "TEST_CODE"))

            # Get the inserted item
            self.cursor.execute("SELECT * FROM inventory WHERE name = ?", (test_name,))
            test_item = self.cursor.fetchone()
            logging.debug(f"Test item: {test_item}")

            # Delete the test item
            self.cursor.execute("DELETE FROM inventory WHERE name = ?", (test_name,))

            # Verify deletion
            self.cursor.execute("SELECT * FROM inventory WHERE name = ?", (test_name,))
            deleted_item = self.cursor.fetchone()
            logging.debug(f"After deletion: {deleted_item}")

            logging.info("Database check completed successfully")
            return True
        except Exception as e:
            logging.error(f"Database check failed: {e}")
            return False

    def add_customer(self, name, contact, address):
        self.cursor.execute("""
            INSERT INTO customers (name, contact, address)
            VALUES (?, ?, ?)
        """, (name, contact, address))
        self.conn.commit()

    def add_transaction(self, date, type, amount, description, reference):
        self.cursor.execute("""
            INSERT INTO transactions (date, type, amount, description, reference)
            VALUES (?, ?, ?, ?, ?)
        """, (date, type, amount, description, reference))
        self.conn.commit()

    def get_customers(self):
        """Get all customers with total and unpaid balances"""
        customers = []

        # Get all customers
        self.cursor.execute("SELECT * FROM customers ORDER BY name")
        base_customers = self.cursor.fetchall()

        for customer in base_customers:
            customer_id = customer[0]

            # Calculate unpaid balance
            unpaid_balance = self.get_customer_unpaid_balance(customer_id)

            # Add customer with unpaid balance
            customers.append(customer + (unpaid_balance,))

        return customers

    def get_customer_unpaid_balance(self, customer_id):
        """Calculate unpaid balance for a customer"""
        try:
            # Check if status column exists
            self.cursor.execute("PRAGMA table_info(invoices)")
            columns = [column[1] for column in self.cursor.fetchall()]

            if 'status' not in columns:
                # If status column doesn't exist, all invoices are considered unpaid
                self.cursor.execute("""
                    SELECT SUM(total_amount) FROM invoices
                    WHERE customer_id = ?
                """, (customer_id,))
            else:
                # Get sum of unpaid invoices
                self.cursor.execute("""
                    SELECT SUM(total_amount) FROM invoices
                    WHERE customer_id = ? AND (status IS NULL OR status = 'Not Paid')
                """, (customer_id,))

            result = self.cursor.fetchone()
            return result[0] if result and result[0] is not None else 0.0

        except Exception as e:
            print(f"Error calculating unpaid balance: {e}")
            return 0.0

    def update_inventory(self, product_id, quantity_change):
        self.cursor.execute("""
            UPDATE inventory
            SET quantity = quantity + ?
            WHERE id = ?
        """, (quantity_change, product_id))
        self.conn.commit()

    def add_supplier(self, name, contact, address):
        """Add a new supplier"""
        try:
            self.cursor.execute("BEGIN TRANSACTION")
            self.cursor.execute("""
                INSERT INTO suppliers (name, contact, address, balance)
                VALUES (?, ?, ?, ?)
            """, (name, contact, address, 0.0))
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.OperationalError as e:
            self.conn.rollback()
            if "readonly database" in str(e).lower():
                raise Exception("Cannot add supplier: Database is read-only. Please check file permissions.")
            else:
                raise Exception(f"Failed to add supplier: {str(e)}")
        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to add supplier: {str(e)}")

    def update_supplier(self, supplier_id, name, contact, address):
        """Update existing supplier"""
        self.cursor.execute("""
            UPDATE suppliers
            SET name=?, contact=?, address=?
            WHERE id=?
        """, (name, contact, address, supplier_id))
        self.conn.commit()

    def get_suppliers(self):
        """Get all suppliers"""
        self.cursor.execute("SELECT * FROM suppliers ORDER BY name")
        return self.cursor.fetchall()

    def get_supplier_by_id(self, supplier_id):
        """Get supplier by ID"""
        self.cursor.execute("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
        return self.cursor.fetchone()

    def get_product_by_id(self, product_id):
        """Get product details by ID"""
        try:
            self.cursor.execute("""
                SELECT id, product_code, name, quantity, unit_price, cost_price,
                       category, min_stock, description
                FROM inventory
                WHERE id = ?
            """, (product_id,))
            return self.cursor.fetchone()
        except Exception as e:
            print(f"Database error in get_product_by_id: {e}")
            return None

    def get_transaction_history(self, start_date, end_date):
        self.cursor.execute("""
            SELECT * FROM transactions
            WHERE date BETWEEN ? AND ?
            ORDER BY date DESC
        """, (start_date, end_date))
        return self.cursor.fetchall()

    def search_products(self, query):
        self.cursor.execute("""
            SELECT * FROM inventory
            WHERE name LIKE ? OR category LIKE ?
        """, (f"%{query}%", f"%{query}%"))
        return self.cursor.fetchall()

    def get_sales_report(self, start_date, end_date):
        self.cursor.execute("""
            SELECT date, SUM(amount) as total
            FROM transactions
            WHERE type = 'SALE' AND date BETWEEN ? AND ?
            GROUP BY date
            ORDER BY date
        """, (start_date, end_date))
        return self.cursor.fetchall()

    def get_inventory_value(self):
        self.cursor.execute("""
            SELECT SUM(quantity * unit_price) as total_value
            FROM inventory
        """)
        return self.cursor.fetchone()[0] or 0.0

    def get_customer_names(self):
        """Get list of customer names for dropdown"""
        self.cursor.execute("SELECT id, name FROM customers ORDER BY name")
        return self.cursor.fetchall()

    def get_customer_by_id(self, customer_id):
        """Get customer details by ID"""
        self.cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
        return self.cursor.fetchone()

    def update_customer_balance(self, customer_id, amount, commit=True):
        """Update customer balance after invoice

        Args:
            customer_id: ID of the customer to update
            amount: Amount to add to balance (negative to decrease)
            commit: Whether to commit the transaction (set to False when called within a transaction)
        """
        self.cursor.execute("""
            UPDATE customers
            SET balance = balance + ?
            WHERE id = ?
        """, (amount, customer_id))

        # Only commit if requested (should be False when called within a transaction)
        if commit:
            self.conn.commit()

    def add_invoice(self, customer_id, items, total, date):
        """Add new invoice with items"""
        try:
            self.cursor.execute("""
                INSERT INTO invoices (customer_id, total_amount, date)
                VALUES (?, ?, ?)
            """, (customer_id, total, date))
            invoice_id = self.cursor.lastrowid

            # Add invoice items
            for item in items:
                self.cursor.execute("""
                    INSERT INTO invoice_items (invoice_id, product_id, quantity, price)
                    VALUES (?, ?, ?, ?)
                """, (invoice_id, item['id'], item['quantity'], item['price']))

            self.conn.commit()
            return invoice_id
        except Exception as e:
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")
            raise e

    def get_customer_invoices(self, customer_id):
        """Get all invoices for a customer"""
        try:
            # Ensure customer_id is an integer
            customer_id = int(customer_id)

            print(f"Debug - Searching for invoices with customer_id: {customer_id}")

            # Get all invoices for the customer
            self.cursor.execute("""
                SELECT i.*, c.name as customer_name
                FROM invoices i
                JOIN customers c ON i.customer_id = c.id
                WHERE i.customer_id = ?
                ORDER BY date DESC
            """, (customer_id,))

            invoices = self.cursor.fetchall()
            print(f"Debug - SQL query executed, found {len(invoices)} invoices")
            print(f"Debug - First invoice data: {invoices[0] if invoices else 'None'}")

            return invoices
        except Exception as e:
            print(f"Error getting customer invoices: {e}")
            return []

    def get_customer_statement(self, customer_id, start_date=None, end_date=None, payment_status=None):
        """Get a customer's account statement with all transactions and detailed invoice information

        Args:
            customer_id: The customer ID
            start_date: Optional start date filter (YYYY-MM-DD)
            end_date: Optional end date filter (YYYY-MM-DD)
            payment_status: Optional payment status filter ('Paid', 'Not Paid', or None for all)

        Returns:
            A tuple containing (customer_info, transactions, invoice_details)
        """
        try:
            # Get customer information
            self.cursor.execute("""
                SELECT id, name, contact, address
                FROM customers
                WHERE id = ?
            """, (customer_id,))
            customer_info = self.cursor.fetchone()

            if not customer_info:
                return None, [], {}

            # Build the query for transactions
            query = """
                SELECT
                    i.id as invoice_id,
                    i.date as transaction_date,
                    i.total_amount as amount,
                    i.status as payment_status,
                    'Invoice' as transaction_type,
                    i.vat_rate,
                    i.vat_amount
                FROM invoices i
                WHERE i.customer_id = ?
            """

            params = [customer_id]

            # Add date filters if provided
            if start_date and end_date:
                query += " AND i.date BETWEEN ? AND ?"
                params.extend([start_date, end_date])
            elif start_date:
                query += " AND i.date >= ?"
                params.append(start_date)
            elif end_date:
                query += " AND i.date <= ?"
                params.append(end_date)

            # Add payment status filter if provided
            if payment_status in ['Paid', 'Not Paid']:
                query += " AND i.status = ?"
                params.append(payment_status)

            query += " ORDER BY i.date DESC"

            # Execute the query
            self.cursor.execute(query, params)
            transactions = self.cursor.fetchall()

            # Get detailed information for each invoice
            invoice_details = {}
            for transaction in transactions:
                invoice_id = transaction[0]  # invoice_id is the first column

                # Get items for this invoice
                self.cursor.execute("""
                    SELECT
                        ii.item_name,
                        ii.description,
                        ii.quantity,
                        ii.price,
                        (ii.quantity * ii.price) as item_total
                    FROM invoice_items ii
                    WHERE ii.invoice_id = ?
                    ORDER BY ii.id
                """, (invoice_id,))

                items = self.cursor.fetchall()

                # Store the items for this invoice
                invoice_details[invoice_id] = items

            # Calculate account summary
            total_invoiced = sum(float(t[2]) for t in transactions) if transactions else 0
            total_paid = sum(float(t[2]) for t in transactions if t[3] == 'Paid') if transactions else 0
            total_unpaid = sum(float(t[2]) for t in transactions if t[3] != 'Paid') if transactions else 0

            # Add account summary to customer_info
            customer_info = list(customer_info) + [total_invoiced, total_paid, total_unpaid]

            return customer_info, transactions, invoice_details
        except Exception as e:
            print(f"Error getting customer statement: {e}")
            return None, [], {}

    def update_invoice_status(self, invoice_id, status):
        """Update the status of an invoice (Paid or Not Paid)"""
        try:
            # Start transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Check if status column exists
            self.cursor.execute("PRAGMA table_info(invoices)")
            columns = [column[1] for column in self.cursor.fetchall()]

            # Add status column if it doesn't exist
            if 'status' not in columns:
                self.cursor.execute("ALTER TABLE invoices ADD COLUMN status TEXT DEFAULT 'Not Paid'")

            # Update the invoice status
            self.cursor.execute("""
                UPDATE invoices
                SET status = ?
                WHERE id = ?
            """, (status, invoice_id))

            self.conn.commit()
            return True
        except sqlite3.OperationalError as e:
            self.conn.rollback()
            if "readonly database" in str(e).lower():
                print(f"Cannot update invoice status: Database is read-only. Please check file permissions.")
                raise Exception("Cannot update invoice status: Database is read-only. Please check file permissions.")
            else:
                print(f"Error updating invoice status: {e}")
                raise Exception(f"Failed to update invoice status: {str(e)}")
        except Exception as e:
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")
            print(f"Error updating invoice status: {e}")
            raise Exception(f"Failed to update invoice status: {str(e)}")

    def get_invoice_status(self, invoice_id):
        """Get the status of an invoice"""
        try:
            # Check if status column exists
            self.cursor.execute("PRAGMA table_info(invoices)")
            columns = [column[1] for column in self.cursor.fetchall()]

            if 'status' not in columns:
                return "Not Paid"  # Default status if column doesn't exist

            # Get the invoice status
            self.cursor.execute("""
                SELECT status FROM invoices
                WHERE id = ?
            """, (invoice_id,))

            result = self.cursor.fetchone()
            if result and result[0]:
                return result[0]
            else:
                return "Not Paid"  # Default status if not set
        except Exception as e:
            print(f"Error getting invoice status: {e}")
            return "Not Paid"  # Default status on error

    def get_invoice_by_id(self, invoice_id):
        """Get invoice details by ID"""
        self.cursor.execute("""
            SELECT * FROM invoices WHERE id = ?
        """, (invoice_id,))
        return self.cursor.fetchone()

    def get_invoice_items(self, invoice_id):
        """Get all items for a specific invoice"""
        try:
            # Debug - print the invoice ID
            print(f"Debug - Getting items for invoice #{invoice_id}")

            # Execute the query with detailed column selection
            self.cursor.execute("""
                SELECT
                    i.id, i.product_code, i.name,
                    ii.quantity, -- Use quantity from invoice_items
                    i.unit_price, i.cost_price, i.category, i.min_stock, i.description,
                    ii.item_name, ii.description, ii.price,
                    ii.product_id, ii.id as invoice_item_id
                FROM invoice_items ii
                LEFT JOIN inventory i ON i.id = ii.product_id
                WHERE ii.invoice_id = ?
                ORDER BY ii.id
            """, (invoice_id,))

            items = self.cursor.fetchall()

            # Debug - print the number of items found
            print(f"Debug - Found {len(items)} items for invoice #{invoice_id}")

            # Debug - print column names
            column_names = [description[0] for description in self.cursor.description]
            print(f"Debug - Column names: {column_names}")

            return items
        except Exception as e:
            print(f"Error getting invoice items: {e}")
            return []

    def get_invoice_details(self, invoice_id):
        """Get full invoice details including items"""
        # Get invoice header
        self.cursor.execute("""
            SELECT i.*, c.name as customer_name, c.contact, c.address
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE i.id = ?
        """, (invoice_id,))
        invoice = self.cursor.fetchone()

        if not invoice:
            return None

        # Get invoice items
        items = self.get_invoice_items(invoice_id)

        # Check if we need to add vat_rate and vat_amount
        self.cursor.execute("PRAGMA table_info(invoices)")
        columns = [column[1] for column in self.cursor.fetchall()]

        # Create a dictionary for the invoice data
        invoice_dict = {}
        for i, col in enumerate(self.cursor.description):
            invoice_dict[col[0]] = invoice[i]

        # Add default values for vat_rate and vat_amount if they don't exist
        if 'vat_rate' not in columns:
            invoice_dict['vat_rate'] = 0.0

        if 'vat_amount' not in columns:
            invoice_dict['vat_amount'] = 0.0

        return {
            'invoice': invoice_dict,
            'items': items
        }

    def add_product(self, name, quantity, unit_price, category, min_stock=0, cost_price=0, product_code=None, description=''):
        """Add a new product to inventory"""
        try:
            # Begin transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Print debug info
            print(f"Adding product: {name}, code: {product_code}, qty: {quantity}")

            # Insert the product
            self.cursor.execute("""
                INSERT INTO inventory (
                    product_code, name, quantity, unit_price, cost_price,
                    category, min_stock, description
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                product_code, name, quantity, unit_price, cost_price,
                category, min_stock, description
            ))

            # Get the new product ID
            product_id = self.cursor.lastrowid
            print(f"New product ID: {product_id}")

            # Verify the product was added
            self.cursor.execute("SELECT * FROM inventory WHERE id = ?", (product_id,))
            new_product = self.cursor.fetchone()
            print(f"Verification - New product: {new_product}")

            # Commit the transaction
            self.cursor.execute("COMMIT")
            print("Transaction committed successfully")

            return product_id
        except sqlite3.IntegrityError as e:
            # Rollback on error
            self.cursor.execute("ROLLBACK")
            print(f"IntegrityError: {e}")
            if "UNIQUE constraint failed" in str(e):
                raise Exception("Product code already exists")
            raise Exception(f"Database integrity error: {str(e)}")
        except Exception as e:
            # Rollback on error
            self.cursor.execute("ROLLBACK")
            print(f"Exception: {e}")
            raise Exception(f"Failed to add product: {str(e)}")

    def update_product(self, product_id, data):
        """Update existing product"""
        try:
            # Begin transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Print debug info
            print(f"Updating product ID: {product_id} with data: {data}")

            # Update the product
            self.cursor.execute("""
                UPDATE inventory
                SET name=?, quantity=?, unit_price=?, cost_price=?,
                    category=?, min_stock=?, product_code=?, description=?
                WHERE id=?
            """, (
                data['name'],
                data['quantity'],
                data['price'],
                data['cost_price'],
                data['category'],
                data['min_stock'],
                data['product_code'],
                data['description'],
                product_id
            ))

            # Verify the update
            rows_affected = self.cursor.rowcount
            print(f"Rows affected: {rows_affected}")

            if rows_affected == 0:
                raise Exception(f"No product found with ID {product_id}")

            # Verify the product was updated
            self.cursor.execute("SELECT * FROM inventory WHERE id = ?", (product_id,))
            updated_product = self.cursor.fetchone()
            print(f"Verification - Updated product: {updated_product}")

            # Commit the transaction
            self.cursor.execute("COMMIT")
            print("Transaction committed successfully")

            return True
        except sqlite3.IntegrityError as e:
            # Rollback on error
            self.cursor.execute("ROLLBACK")
            print(f"IntegrityError: {e}")
            if "UNIQUE constraint failed" in str(e):
                raise Exception("Product code already exists")
            raise Exception(f"Database integrity error: {str(e)}")
        except Exception as e:
            # Rollback on error
            self.cursor.execute("ROLLBACK")
            print(f"Exception: {e}")
            raise Exception(f"Failed to update product: {str(e)}")

    def get_all_inventory(self):
        """Get all inventory items"""
        self.cursor.execute("""
            SELECT id, product_code, name, quantity, unit_price, cost_price,
                   category, min_stock, description
            FROM inventory
            ORDER BY name
        """)
        return self.cursor.fetchall()

    def get_next_invoice_number(self):
        """Get next available invoice number"""
        self.cursor.execute("SELECT MAX(id) FROM invoices")
        last_id = self.cursor.fetchone()[0] or 0
        return last_id + 1

    def save_invoice(self, invoice_data):
        """Save complete invoice with items"""
        try:
            invoice_id = self.add_invoice(
                customer_id=invoice_data['customer_id'],
                items=invoice_data['items'],
                total=invoice_data['total'],
                date=invoice_data['date']
            )
            return invoice_id
        except Exception as e:
            raise Exception(f"Failed to save invoice: {str(e)}")

    def get_low_stock(self, threshold):
        """Get items below threshold"""
        self.cursor.execute("""
            SELECT * FROM inventory
            WHERE quantity <= ?
            ORDER BY quantity ASC
        """, (threshold,))
        return self.cursor.fetchall()

    def create_purchase_order(self, supplier_id, items, total):
        """Create a purchase order with items"""
        try:
            # Start transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Create purchase order record
            self.cursor.execute("""
                INSERT INTO purchase_orders (supplier_id, total_amount, date, status)
                VALUES (?, ?, date('now'), 'Pending')
            """, (supplier_id, total))

            order_id = self.cursor.lastrowid

            # Add order items
            for item in items:
                self.cursor.execute("""
                    INSERT INTO purchase_order_items (order_id, product_id, quantity, cost_price, total_cost)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    order_id,
                    item['product_id'],
                    item['quantity'],
                    item['cost_price'],
                    item['total']
                ))

            # Commit transaction
            self.conn.commit()
            return order_id

        except Exception as e:
            # Rollback on error
            self.conn.rollback()
            raise Exception(f"Failed to create purchase order: {str(e)}")

    def receive_purchase_order(self, order_id):
        """Mark purchase order as received and update inventory"""
        try:
            # Start transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Get order items
            self.cursor.execute("""
                SELECT product_id, quantity FROM purchase_order_items
                WHERE order_id = ?
            """, (order_id,))

            items = self.cursor.fetchall()

            # Update inventory for each item
            for product_id, quantity in items:
                self.cursor.execute("""
                    UPDATE inventory
                    SET quantity = quantity + ?
                    WHERE id = ?
                """, (quantity, product_id))

            # Update order status
            self.cursor.execute("""
                UPDATE purchase_orders
                SET status = 'Received', received_date = date('now')
                WHERE id = ?
            """, (order_id,))

            # Commit transaction
            self.conn.commit()

        except Exception as e:
            # Rollback on error
            self.conn.rollback()
            raise Exception(f"Failed to receive purchase order: {str(e)}")

    def get_sales_average(self, days=30):
        """Get average daily sales"""
        self.cursor.execute("""
            SELECT AVG(daily_total)
            FROM (
                SELECT date, SUM(amount) as daily_total
                FROM transactions
                WHERE type = 'SALE'
                AND date >= date('now', ?)
                GROUP BY date
            )
        """, (f'-{days} days',))
        return self.cursor.fetchone()[0] or 0.0

    def delete_customer(self, customer_id):
        """Delete customer if no invoices exist"""
        self.cursor.execute("SELECT COUNT(*) FROM invoices WHERE customer_id = ?", (customer_id,))
        if self.cursor.fetchone()[0] > 0:
            raise ValueError("Cannot delete customer with existing invoices")
        self.cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
        self.conn.commit()

    def delete_product(self, product_id):
        """Delete product if no invoice items exist"""
        try:
            # Begin transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Check if product is used in invoices
            self.cursor.execute("SELECT COUNT(*) FROM invoice_items WHERE product_id = ?", (product_id,))
            if self.cursor.fetchone()[0] > 0:
                raise ValueError("Cannot delete product used in invoices")

            # Delete the product
            self.cursor.execute("DELETE FROM inventory WHERE id = ?", (product_id,))

            # Verify deletion
            rows_affected = self.cursor.rowcount
            print(f"Rows affected by delete: {rows_affected}")

            if rows_affected == 0:
                raise ValueError(f"No product found with ID {product_id}")

            # Commit the transaction
            self.cursor.execute("COMMIT")
            print(f"Product {product_id} deleted successfully")

            return True
        except Exception as e:
            # Rollback on error, but only if a transaction is active
            try:
                self.cursor.execute("ROLLBACK")
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")
            print(f"Error deleting product: {e}")
            raise e

    def delete_supplier(self, supplier_id):
        self.cursor.execute("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
        self.conn.commit()

    def delete_invoice(self, invoice_id):
        """Delete an invoice and its items"""
        # Flag to track if transaction was started
        transaction_started = False

        try:
            # Ensure invoice_id is an integer
            invoice_id = int(invoice_id)

            # Start transaction
            self.cursor.execute("BEGIN TRANSACTION")
            transaction_started = True
            print("Transaction started successfully")

            # Debug: Print all invoices to see what's available
            self.cursor.execute("SELECT id FROM invoices")
            all_invoices = self.cursor.fetchall()
            print(f"Available invoice IDs: {[inv[0] for inv in all_invoices]}")

            # Get invoice details to update customer balance
            self.cursor.execute("SELECT customer_id, total_amount, status FROM invoices WHERE id = ?", (invoice_id,))
            invoice = self.cursor.fetchone()

            if not invoice:
                # Try to find the invoice with a different query
                self.cursor.execute("SELECT * FROM invoices WHERE id = ?", (invoice_id,))
                test_invoice = self.cursor.fetchone()
                if test_invoice:
                    print(f"Found invoice with different query: {test_invoice}")
                else:
                    print(f"Invoice #{invoice_id} not found with any query")
                raise ValueError(f"Invoice #{invoice_id} not found")

            customer_id = invoice[0]
            total_amount = invoice[1]
            status = invoice[2] if len(invoice) > 2 else "Not Paid"

            print(f"Found invoice #{invoice_id} for customer #{customer_id} with amount {total_amount} and status {status}")

            # If invoice was paid, we need to adjust the customer balance
            if status == "Paid":
                # Decrease customer balance (negative amount increases balance)
                # Pass commit=False to prevent premature commit within the transaction
                self.update_customer_balance(customer_id, -total_amount, commit=False)
                print(f"Updated customer #{customer_id} balance by -{total_amount}")

            # Delete invoice items first (foreign key constraint)
            self.cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
            print(f"Deleted {self.cursor.rowcount} invoice items")

            # Delete the invoice
            self.cursor.execute("DELETE FROM invoices WHERE id = ?", (invoice_id,))
            deleted_count = self.cursor.rowcount
            print(f"Deleted {deleted_count} invoices")

            # If no invoices were deleted, it might be because the invoice doesn't exist
            # but we already checked for that, so this is just an extra safeguard
            if deleted_count == 0:
                print(f"Warning: No invoices were deleted with ID {invoice_id}")

            # Commit the transaction
            print("Committing transaction...")
            self.cursor.execute("COMMIT")
            print("Transaction committed successfully")
            transaction_started = False  # Reset flag after successful commit
            return True

        except Exception as e:
            # Rollback on error, but only if a transaction is active
            if transaction_started:
                try:
                    print("Rolling back transaction due to error...")
                    self.cursor.execute("ROLLBACK")
                    print("Transaction rolled back successfully")
                except Exception as rollback_error:
                    print(f"Rollback failed: {rollback_error}")
                    # Continue with the original error

            print(f"Error deleting invoice: {e}")
            raise Exception(f"Failed to delete invoice: {str(e)}")

    def get_product_details(self, product_id):
        """Get complete product details"""
        self.cursor.execute("""
            SELECT * FROM inventory WHERE id = ?
        """, (product_id,))
        return self.cursor.fetchone()

    def get_all_products_for_sale(self):
        """Get all available products with stock and price"""
        try:
            # First, let's debug what's in the inventory
            self.cursor.execute("SELECT COUNT(*) FROM inventory")
            total_count = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity > 0")
            with_stock = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM inventory WHERE unit_price > 0")
            with_price = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity > 0 AND unit_price > 0")
            eligible = self.cursor.fetchone()[0]

            print(f"Debug DB - Total products: {total_count}, With stock: {with_stock}, With price: {with_price}, Eligible for sale: {eligible}")

            # Now get the products for sale
            self.cursor.execute("""
                SELECT id, name, quantity, unit_price
                FROM inventory
                WHERE quantity > 0 AND unit_price > 0
                ORDER BY name
            """)
            products = self.cursor.fetchall()
            print(f"Debug DB - Returning {len(products)} products for sale")
            return products
        except Exception as e:
            print(f"Database error in get_all_products_for_sale: {e}")
            return []

    def check_stock_available(self, product_id, quantity):
        """Verify if enough stock is available"""
        self.cursor.execute("""
            SELECT quantity FROM inventory
            WHERE id = ? AND quantity >= ?
        """, (product_id, quantity))
        return bool(self.cursor.fetchone())

    def update_invoice(self, invoice_id, customer_id, items, total, vat_rate=0, discount_amount=0, discount_type='amount', deposit_amount=0, deposit_date=None):
        """Update an existing invoice with new items and totals"""
        try:
            # Start transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Calculate VAT amount from the total (which already includes VAT)
            # To extract VAT from total: VAT = Total - (Total / (1 + VAT_rate/100))
            vat_amount = total - (total / (1 + vat_rate/100))

            # Update the invoice header
            self.cursor.execute("""
                UPDATE invoices
                SET customer_id = ?, total_amount = ?, vat_rate = ?, vat_amount = ?,
                    discount_amount = ?, discount_type = ?, deposit_amount = ?, deposit_date = ?
                WHERE id = ?
            """, (customer_id, total, vat_rate, vat_amount, discount_amount, discount_type, deposit_amount, deposit_date, invoice_id))

            # Delete existing invoice items
            self.cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))

            # Add new invoice items
            for item in items:
                # Check if this is a product from inventory or a custom item
                product_id = None
                if 'id' in item and item['id']:
                    product_id = item['id']

                # Add the invoice item
                self.cursor.execute("""
                    INSERT INTO invoice_items
                    (invoice_id, product_id, quantity, price, item_name, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (invoice_id, product_id, item['quantity'], item['price'],
                      item['name'], item.get('description', '')))

                print(f"Updated invoice item: {item['name']} x {item['quantity']}")

                # Update inventory only if product exists in inventory
                if product_id is not None:
                    # For inventory updates in an invoice update, we need to:
                    # 1. Get the original quantity in the invoice before deletion
                    # 2. Get the new quantity being added
                    # 3. Update inventory based on the difference (new - original)

                    # We don't need to adjust inventory for updates since we:
                    # 1. Deleted all invoice items (which doesn't affect inventory)
                    # 2. Added new items (which also doesn't affect inventory in this context)
                    # The inventory was already adjusted when the invoice was first created

                    # Just log that we're skipping inventory update for existing products
                    print(f"Skipping inventory update for product '{item['name']}' (ID: {product_id}) - inventory already adjusted when invoice was created")

                    print(f"Updated inventory for product '{item['name']}' (ID: {product_id}), adjusted by {item['quantity']}")

            # Commit the transaction
            self.cursor.execute("COMMIT")
            print(f"Transaction committed successfully for updated invoice #{invoice_id}")
            return invoice_id

        except sqlite3.OperationalError as e:
            # Handle specific SQLite errors
            try:
                self.cursor.execute("ROLLBACK")
                print(f"Transaction rolled back due to SQLite error: {e}")
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")

            # Check for readonly database error
            if "readonly database" in str(e).lower():
                error_msg = "Cannot update invoice: Database is read-only. Please check file permissions."
                print(error_msg)
                raise Exception(error_msg)
            else:
                raise Exception(f"Failed to update invoice: {str(e)}")
        except Exception as e:
            # Ensure rollback happens for other errors
            try:
                self.cursor.execute("ROLLBACK")
                print(f"Transaction rolled back due to error: {e}")
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")
                # Continue with the original error
            raise Exception(f"Failed to update invoice: {str(e)}")

    def process_sale(self, customer_id, items, total, vat_rate=0, discount_amount=0, discount_type='amount', deposit_amount=0, deposit_date=None):
        """Process a sale with items"""
        try:
            # Start transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Calculate VAT amount from the total (which already includes VAT)
            # To extract VAT from total: VAT = Total - (Total / (1 + VAT_rate/100))
            vat_amount = total - (total / (1 + vat_rate/100))

            # Add invoice with discount and deposit information
            self.cursor.execute("""
                INSERT INTO invoices
                (customer_id, total_amount, date, status, vat_rate, vat_amount, discount_amount, discount_type, deposit_amount, deposit_date)
                VALUES (?, ?, DATE('now'), ?, ?, ?, ?, ?, ?, ?)
            """, (customer_id, total, 'Not Paid', vat_rate, vat_amount, discount_amount, discount_type, deposit_amount, deposit_date))

            invoice_id = self.cursor.lastrowid
            print(f"Created invoice #{invoice_id}")

            # First verify all stock is available (double-check)
            for item in items:
                # Get product details by name
                self.cursor.execute(
                    "SELECT id, quantity FROM inventory WHERE name = ?",
                    (item['name'],)
                )
                result = self.cursor.fetchone()

                # Skip stock check for custom items (not in inventory)
                if not result:
                    print(f"Product '{item['name']}' not found in inventory - treating as custom item")
                    continue

                product_id, current_stock = result
                if current_stock < item['quantity']:
                    try:
                        self.cursor.execute("ROLLBACK")
                    except Exception as rollback_error:
                        print(f"Rollback failed: {rollback_error}")
                    raise Exception(f"Insufficient stock for {item['name']}. Available: {current_stock}, Requested: {item['quantity']}")

            # Add invoice items and update inventory
            for item in items:
                # Try to get product ID by name
                self.cursor.execute(
                    "SELECT id FROM inventory WHERE name = ?",
                    (item['name'],)
                )
                result = self.cursor.fetchone()
                product_id = result[0] if result else None

                # Insert invoice item
                self.cursor.execute("""
                    INSERT INTO invoice_items
                    (invoice_id, product_id, quantity, price, item_name, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    invoice_id,
                    product_id,  # Will be None for custom items
                    item['quantity'],
                    item['price'],
                    item['name'],
                    item.get('description', '')
                ))
                print(f"Added invoice item: {item['name']} x {item['quantity']}")

                # Update inventory only if product exists in inventory
                if product_id is not None:
                    self.cursor.execute("""
                        UPDATE inventory
                        SET quantity = quantity - ?
                        WHERE id = ?
                    """, (item['quantity'], product_id))
                    print(f"Updated inventory for product '{item['name']}' (ID: {product_id}), reduced by {item['quantity']}")

            # Commit the transaction
            self.cursor.execute("COMMIT")
            print(f"Transaction committed successfully for invoice #{invoice_id}")
            return invoice_id
        except sqlite3.OperationalError as e:
            # Handle specific SQLite errors
            try:
                self.cursor.execute("ROLLBACK")
                print(f"Transaction rolled back due to SQLite error: {e}")
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")

            # Check for readonly database error
            if "readonly database" in str(e).lower():
                error_msg = "Cannot process sale: Database is read-only. Please check file permissions."
                print(error_msg)
                raise Exception(error_msg)
            else:
                raise Exception(f"Failed to process sale: {str(e)}")
        except Exception as e:
            # Ensure rollback happens for other errors
            try:
                self.cursor.execute("ROLLBACK")
                print(f"Transaction rolled back due to error: {e}")
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")
                # Continue with the original error
            raise Exception(f"Failed to process sale: {str(e)}")

    def get_settings(self):
        """Get application settings from database"""
        try:
            # First try to get settings from database
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            """)

            # Get all settings
            self.cursor.execute("SELECT key, value FROM settings")
            settings = dict(self.cursor.fetchall())

            # Return settings with defaults if not found
            return {
                'low_stock': int(settings.get('low_stock', 10)),
                'currency': settings.get('currency', '$'),
                'tax_rate': float(settings.get('tax_rate', 15.0))
            }
        except Exception:  # No need to capture the exception variable
            # Return default settings if anything goes wrong
            return {
                'low_stock': 10,
                'currency': '$',
                'tax_rate': 15.0
            }

    def save_settings(self, settings):
        """Save settings to database"""
        try:
            for key, value in settings.items():
                self.cursor.execute("""
                    INSERT OR REPLACE INTO settings (key, value)
                    VALUES (?, ?)
                """, (key, str(value)))
            self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to save settings: {str(e)}")

    def get_settings_value(self, key, default=None):
        """Get a specific setting value from database"""
        try:
            self.cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            result = self.cursor.fetchone()
            return result[0] if result else default
        except Exception as e:
            print(f"Failed to get setting {key}: {e}")
            return default

    def update_settings_value(self, key, value):
        """Update or insert a setting value"""
        try:
            self.cursor.execute("""
                INSERT OR REPLACE INTO settings (key, value)
                VALUES (?, ?)
            """, (key, str(value)))
            self.conn.commit()
        except Exception as e:
            print(f"Failed to update setting {key}: {e}")
            self.conn.rollback()

    def update_customer(self, customer_id, name, contact, address):
        """Update existing customer"""
        try:
            # Begin transaction
            self.cursor.execute("BEGIN TRANSACTION")

            # Update the customer
            self.cursor.execute("""
                UPDATE customers
                SET name=?, contact=?, address=?
                WHERE id=?
            """, (name, contact, address, customer_id))

            if self.cursor.rowcount == 0:
                try:
                    self.conn.rollback()
                except Exception as rollback_error:
                    print(f"Rollback failed: {rollback_error}")
                raise ValueError(f"No customer found with ID {customer_id}")

            # Commit the transaction
            self.conn.commit()
            return True

        except Exception as e:
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Rollback failed: {rollback_error}")
            raise Exception(f"Failed to update customer: {str(e)}")

    def ensure_invoice_items_table(self):
        """Ensure invoice_items table has required columns"""
        self.cursor.execute("""
            ALTER TABLE invoice_items
            ADD COLUMN item_name TEXT;
        """)
        self.conn.commit()

    def mark_invoice_as_paid(self, invoice_id):
        """Mark an invoice as paid"""
        try:
            self.cursor.execute("""
                UPDATE invoices
                SET status = 'Paid'
                WHERE id = ?
            """, (invoice_id,))
            self.conn.commit()
            logging.info(f"Invoice #{invoice_id} marked as paid")
        except Exception as e:
            try:
                self.conn.rollback()
                logging.warning(f"Transaction rolled back due to error: {e}")
            except Exception as rollback_error:
                logging.error(f"Rollback failed: {rollback_error}")
            logging.error(f"Failed to mark invoice as paid: {e}")
            raise Exception(f"Failed to mark invoice as paid: {str(e)}")

    def get_paid_invoices(self):
        """Get a list of all paid invoice IDs

        Returns:
            list: List of paid invoice IDs
        """
        try:
            self.cursor.execute("""
                SELECT id FROM invoices
                WHERE status = 'Paid'
            """)
            results = self.cursor.fetchall()
            paid_invoices = [row[0] for row in results] if results else []
            logging.debug(f"Found {len(paid_invoices)} paid invoices")
            return paid_invoices
        except Exception as e:
            logging.error(f"Error getting paid invoices: {e}")
            return []

    def get_invoice_status(self, invoice_id):
        """Get the status of an invoice

        Args:
            invoice_id: The ID of the invoice

        Returns:
            str: The status of the invoice ('Paid' or 'Not Paid')
        """
        try:
            self.cursor.execute("""
                SELECT status FROM invoices
                WHERE id = ?
            """, (invoice_id,))
            result = self.cursor.fetchone()
            status = result[0] if result else 'Not Paid'
            logging.debug(f"Invoice #{invoice_id} status: {status}")
            return status
        except Exception as e:
            logging.error(f"Error getting invoice status: {e}")
            return 'Not Paid'

    def add_invoice_deposit(self, invoice_id, amount, date, description=""):
        """Add a new deposit to an existing invoice"""
        try:
            self.cursor.execute("""
                INSERT INTO invoice_deposits (invoice_id, amount, date, description)
                VALUES (?, ?, ?, ?)
            """, (invoice_id, amount, date, description))

            self.conn.commit()
            print(f"Added deposit of {amount} to invoice #{invoice_id} on {date}")
            return self.cursor.lastrowid

        except Exception as e:
            print(f"Error adding deposit: {e}")
            self.conn.rollback()
            return None

    def get_invoice_deposits(self, invoice_id):
        """Get all deposits for a specific invoice"""
        try:
            self.cursor.execute("""
                SELECT id, amount, date, description, created_at
                FROM invoice_deposits
                WHERE invoice_id = ?
                ORDER BY date ASC
            """, (invoice_id,))

            return self.cursor.fetchall()

        except Exception as e:
            print(f"Error getting invoice deposits: {e}")
            return []

    def get_total_deposits_for_invoice(self, invoice_id):
        """Get the total amount of all deposits for an invoice"""
        try:
            self.cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) as total_deposits
                FROM invoice_deposits
                WHERE invoice_id = ?
            """, (invoice_id,))

            result = self.cursor.fetchone()
            return result[0] if result else 0

        except Exception as e:
            print(f"Error getting total deposits: {e}")
            return 0

    def remove_invoice_deposit(self, deposit_id):
        """Remove a specific deposit"""
        try:
            self.cursor.execute("""
                DELETE FROM invoice_deposits
                WHERE id = ?
            """, (deposit_id,))

            self.conn.commit()
            print(f"Removed deposit #{deposit_id}")
            return True

        except Exception as e:
            print(f"Error removing deposit: {e}")
            self.conn.rollback()
            return False
